// 游戏配置文件 - 基于最新游戏界面设计文档

module.exports = {
  // 关卡配置
  levels: [
    { id: 1, title: "萌宠新手村", targetScore: 1000, petTypes: 5 },
    { id: 2, title: "萌宠总动员", targetScore: 4000, petTypes: 7 },
    { id: 3, title: "萌宠修罗场", targetScore: 8000, petTypes: 9 }
  ],

  // 萌宠类型配置 (与文档中背景色对应)
  pets: [
    { type: "cat", color: "#FFE5E5" },
    { type: "dog", color: "#E5F5FF" },
    { type: "fox", color: "#E5FFEB" },
    { type: "frog", color: "#FFF7E5" },
    { type: "lion", color: "#F5E5FF" },
    { type: "monkey", color: "#FFE5F5" },
    { type: "panda", color: "#E5FFE5" },
    { type: "rabbit", color: "#E5E5FF" },
    { type: "tiger", color: "#FFF0E5" }
  ],

  // 特殊道具配置
  props: {
    rocket: { color: "#FF4500", effect: "vertical消除" },
    bomb: { color: "#8B0000", effect: "3x3爆炸" }
  },

  // 游戏平衡参数
  balance: {
    comboMultipliers: {
      base: 1.5,       // 3次以下连击倍率
      high: 2.0        // 5次以上连击倍率
    },
    scoreValues: {
      3: 30,          // 3连消基础分
      4: 40,          // 4连消基础分
      5: 50           // 5连消基础分
    }
  },

  // 性能优化配置
  performance: {
    textureBatching: true,  // 启用纹理合批
    objectPoolSize: 50,     // 对象池大小
    asyncLoading: true      // 启用异步加载
  }
};