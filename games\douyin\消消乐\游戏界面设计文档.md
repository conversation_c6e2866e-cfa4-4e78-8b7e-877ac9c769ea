# 游戏界面与玩法设计方案（基于抖音原生开发特性）

**核心目标**：轻量化资源（包体≤15MB） + 高流畅性（帧率≥50FPS） + 强社交互动（抖音API深度集成）

## 一、界面设计
### 1. 游戏主界面（Gameplay）

#### 布局分区：

##### 顶部统计栏（15%高度）
- **关卡标题**：动态显示关卡名称（如“第一关：萌宠新手村”），字体为圆润卡通风格，颜色随进度变化（初始蓝→通关金）
- **分数/目标/进度**：
  - 分数：实时更新，高亮显示当前得分（如“🎯目标：5000 | 当前：3200”）
  - 进度条：萌宠爪印填充效果，通关时触发烟花动画
- **连击系统**：
  - 连击数动态显示为火焰图标（🔥×3），连击≥5时火焰变为橙红色，倍率叠加（1.5×→2.0×）
  - 连击中断时触发震动反馈

##### 中部网格区域（70%高度）
- **8×10网格**：
  - 格子尺寸：128×128px（适配图片尺寸），间隔2px，边框虚线（#D3D3D3）
  - 萌宠图标：9种基础类型（猫、狗、狮、狐、蛙、猴、熊、兔、虎），采用128×128px PNG格式，按关卡分阶段加载：
    - 第一关：5种随机萌宠
    - 第二关：7种随机萌宠
    - 第三关：全部9种萌宠
  - 背景色规范：
    - 萌宠背景色（9种不重复浅色，按萌宠类型分配）：
      - cat.png：#FFE5E5（浅粉色）
      - dog.png：#E5F5FF（浅蓝色）
      - fox.png：#E5FFEB（浅绿色）
      - frog.png：#FFF7E5（浅黄色）
      - lion.png：#F5E5FF（浅紫色）
      - monkey.png：#FFE5F5（浅玫红色）
      - panda.png：#E5FFE5（淡绿色）
      - rabbit.png：#E5E5FF（淡蓝色）
      - tiger.png：#FFF0E5（浅橙色）
    - 特殊道具背景色（鲜明对比色）：
      - 火箭：#FF4500（橙红色）
      - 炸弹：#8B0000（深红色）
  - 采用矢量动画（Spine骨骼动画）减少内存占用
- **交互反馈**：
  - 拖拽时：源格子半透明化，目标格子高亮（颜色#FFA07A，透明度50%）
  - 消除动画：3消触发爱心粒子爆炸，5消触发全屏流星雨特效（Lottie动画，≤10帧）

##### 底部道具栏（15%高度）
- **道具卡**：横向排列，图标+数量显示（如“×3”）：
  - 刷新卡：旋转箭头图标，点击后网格随机洗牌（动画：旋风效果）
  - 炸弹卡：拖拽至网格触发5×5爆炸范围预渲染（红色高亮框），爆炸后播放烟雾粒子
  - 清屏卡：全屏半透明遮罩覆盖，点击后清除所有萌宠（动画：扫帚横扫）
  - 降级卡生效机制：当用户使用降级卡后立即生效并刷新网格。重新排序后游戏规则和玩法保持不变。

### 2. 社交功能集成
- **实时好友互动**：通过抖音API `tt.getFriendRanking` 显示好友当前得分与排名（悬浮于网格右侧）
- **复活邀请**：失败时弹窗“邀请好友助力复活”，点击后分享至抖音聊天，好友点击触发刷新卡效果（不消耗次数）

## 二、玩法机制设计
### 1. 核心玩法
#### 消除规则：
- 基础消除：3/4/5消分别对应30/40/50分，4消生成纵向火箭，5消生成3×3炸弹
- 特殊组合：
  - 火箭+火箭：触发十字消除（+200分），动画为光束交叉扫射
  - 炸弹+炸弹：5×5爆炸（+500分），伴随屏幕震动
  - 小火箭+小火箭：横向消除整行
  - 小炸弹+小炸弹：3x3范围爆炸
#### 连击系统：
- 主动连击：2秒内连续消除触发连击，每次消除倍率递增（1.5×→2.0×）
- 中断机制：若未在时限内消除，连击数归零并播放“连击失败”音效
- 连击倍率计算规则：3次以下1.5倍，5次以上2.0倍
#### 特殊方块规则：
- 火箭消除方向定义：纵向消除所在列所有萌宠
- 特殊方块交换规则：
  - 小火箭交换效果：
    1. 与普通格子交换：消除所在列，+100分
    2. 与小火箭交换：触发十字消除（一行+一列），+300分
    3. 与小炸弹交换：消除3整列，+400分
  - 小炸弹交换效果：
    1. 与普通格子交换：3x3区域爆炸消除，+200分
    2. 与小炸弹交换：5x5范围爆炸消除，+500分
#### 连消分数计算：
- 普通3连消：30分
- 普通4连消：40分
- 普通5连消：50分
- 每增加1连消额外+10分

### 2. 关卡逻辑
#### 关卡配置：
- **第一关**：标题《萌宠新手村》，目标分数1000分
- **第二关**：标题《萌宠总动员》，目标分数4000分
- **第三关**：标题《萌宠修罗场》，目标分数8000分
#### 关卡初始化算法：
- 采用随机填充算法生成网格，但需确保初始化时无3连消或更多连消
- 实现逻辑：
  1. 随机填充网格并检查所有行和列
  2. 若检测到3连消则重新生成该区域
  3. 重复检查直至满足条件
#### 胜利与失败条件：
- **胜利条件**：分数≥目标值后播放通关动画，3秒后自动跳转下一关
- **失败条件**：
  - 无解检测：异步线程运行DFS算法检测可解性
  - 复活机制：每局限2次复活，触发后网格刷新但目标分数不变

### 3. 社交化扩展
- **直播联动**：集成抖音直播SDK，观众弹幕发送“发射火箭”可触发随机道具掉落
- **UGC激励**：达成高连击（≥5次）自动生成15秒短视频，支持一键分享至抖音话题#萌宠连击王

## 三、性能优化方案
### 资源管理：
- 动态加载：按关卡分阶段加载萌宠贴图（128×128px WebP格式，压缩率80%），非活跃资源即时卸载
- 纹理合批：使用TexturePacker生成128×128px图集（每张萌宠图128×128px，支持1×1个图标），通过SpriteBatch实现单Draw Call渲染

### 渲染优化：
- Native原生渲染：使用平台GPU加速API（Android OpenGL ES / iOS Metal）绘制萌宠纹理
- GPU Instancing：合并粒子特效实例（爆炸烟雾、爱心粒子），减少GPU负载
- 异步动画：连击光效与消除动画分离至Worker线程，避免主线程阻塞

### 低端机适配：
- 内存≤2GB设备自动关闭高级特效（3D粒子→2D径向渐变）
- 纹理分辨率降级至1x（60×60px→30×30px）

## 四、无障碍设计
- **高对比模式**：萌宠图标增加2px白色描边，文字对比度≥4.5:1（背景#F5F5F5，文字#333333）
- **触控扩展**：实际点击区域扩大至图标外围10px，适配误触场景

## 引用依据：
- 界面分层设计参考网页8的手持设备UI模型与网页16的UI模式转换方法
- 社交功能集成结合网页1的抖音直播交互分析与网页14的泰国版用户调研
- 性能优化策略借鉴网页7的Unity引擎优化与网页25的PPO算法异步处理逻辑
此方案兼顾抖音小游戏的社交特性与原生性能要求，通过模块化设计与资源优化确保包体轻量化，适合快速迭代与多端适配。