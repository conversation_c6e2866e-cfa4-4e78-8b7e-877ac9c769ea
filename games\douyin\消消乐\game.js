// 游戏主逻辑 - 基于最新游戏界面设计文档
const config = require('./config.js');
let systemInfo, canvas, ctx;
let gameState = { level: 1, score: 0, combo: 0, grid: [] };

// 初始化游戏
function initGame() {
  systemInfo = tt.getSystemInfoSync();
  canvas = tt.createCanvas();
  ctx = canvas.getContext('2d');
  canvas.width = systemInfo.windowWidth;
  canvas.height = systemInfo.windowHeight;
  
  // 初始化网格
  gameState.grid = generateGrid(config.levels[gameState.level - 1].petTypes);
  // 设置触摸事件
  setupTouchEvents();
  renderGame();
}

// 生成初始网格（无初始连消）
function generateGrid(petTypesCount) {
  const grid = [];
  const size = { rows: 8, cols: 10 };
  
  // 随机填充网格
  for (let y = 0; y < size.rows; y++) {
    grid[y] = [];
    for (let x = 0; x < size.cols; x++) {
      let typeId;
      do {
        typeId = Math.floor(Math.random() * petTypesCount);
      } while (hasInitialMatch(grid, x, y, typeId, size));
      grid[y][x] = { type: config.pets[typeId].type, color: config.pets[typeId].color };
    }
  }
  return grid;
}

// 检查初始网格是否有连消
function hasInitialMatch(grid, x, y, typeId, size) {
  // 检查横向
  if (x >= 2 && 
      grid[y][x-1]?.type === config.pets[typeId].type && 
      grid[y][x-2]?.type === config.pets[typeId].type) return true;
  
  // 检查纵向
  if (y >= 2 && 
      grid[y-1][x]?.type === config.pets[typeId].type && 
      grid[y-2][x]?.type === config.pets[typeId].type) return true;
  
  return false;
}

// 渲染游戏界面
function renderGame() {
  // 清空画布
  ctx.fillStyle = '#F5F5F5';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // 绘制网格
  const cellSize = Math.min(canvas.width / 10, canvas.height * 0.7 / 8);
  const startX = (canvas.width - cellSize * 10) / 2;
  const startY = canvas.height * 0.15; // 顶部统计栏占15%
  
  gameState.grid.forEach((row, y) => {
    row.forEach((cell, x) => {
      // 绘制萌宠格子
      ctx.fillStyle = cell.color;
      ctx.beginPath();
      ctx.roundRect(startX + x * cellSize, startY + y * cellSize, cellSize - 2, cellSize - 2, 8);
      ctx.fill();
      
      // 绘制萌宠类型文字（实际项目中替换为图片）
      ctx.fillStyle = '#333';
      ctx.font = `${cellSize * 0.5}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(cell.type[0].toUpperCase(), startX + x * cellSize + cellSize/2, startY + y * cellSize + cellSize/2);
    });
  });
  
  // 绘制分数和连击
  renderHUD();
}

// 绘制头部信息显示
function renderHUD() {
  // 分数显示
  ctx.fillStyle = '#333';
  ctx.font = `${canvas.height * 0.05}px Arial`;
  ctx.textAlign = 'left';
  ctx.fillText(`分数: ${gameState.score}`, canvas.width * 0.05, canvas.height * 0.08);
  
  // 连击显示
  if (gameState.combo > 0) {
    ctx.fillStyle = gameState.combo >= 5 ? '#FF4500' : '#FFA500';
    ctx.fillText(`连击×${gameState.combo} (${getComboMultiplier()}x)`, canvas.width * 0.5, canvas.height * 0.08);
  }
}

// 获取连击倍率
function getComboMultiplier() {
  return gameState.combo < 3 ? 1 : gameState.combo < 5 ? config.balance.comboMultipliers.base : config.balance.comboMultipliers.high;
}

// 检测并处理消除
function checkAndProcessMatches() {
  const matches = [];
  const size = { rows: gameState.grid.length, cols: gameState.grid[0].length };
  
  // 检测横向匹配
  for (let y = 0; y < size.rows; y++) {
    let x = 0;
    while (x < size.cols - 2) {
      const currentType = gameState.grid[y][x].type;
      if (currentType && 
          gameState.grid[y][x+1].type === currentType && 
          gameState.grid[y][x+2].type === currentType) {
        // 找到匹配，检查更长连消
        let endX = x + 2;
        while (endX + 1 < size.cols && gameState.grid[y][endX+1].type === currentType) endX++;
        matches.push({ type: 'horizontal', x, y, length: endX - x + 1 });
        x = endX + 1;
      } else {
        x++;
      }
    }
  }
  
  // 检测纵向匹配
  for (let x = 0; x < size.cols; x++) {
    let y = 0;
    while (y < size.rows - 2) {
      const currentType = gameState.grid[y][x].type;
      if (currentType && 
          gameState.grid[y+1][x].type === currentType && 
          gameState.grid[y+2][x].type === currentType) {
        // 找到匹配，检查更长连消
        let endY = y + 2;
        while (endY + 1 < size.rows && gameState.grid[endY+1][x].type === currentType) endY++;
        matches.push({ type: 'vertical', x, y, length: endY - y + 1 });
        y = endY + 1;
      } else {
        y++;
      }
    }
  }
  
  if (matches.length > 0) {
    processMatches(matches);
    return true;
  } else {
    // 没有匹配，重置连击
    gameState.combo = 0;
    return false;
  }
}

// 处理匹配消除和分数计算
function processMatches(matches) {
  // 增加连击数
  gameState.combo++;
  
  // 计算分数
  let matchScore = 0;
  matches.forEach(match => {
    // 基础分数 + 长度奖励
    const baseScore = config.balance.scoreValues[3] + (match.length - 3) * 10;
    // 应用连击倍率
    matchScore += baseScore * getComboMultiplier();
    
    // 标记消除位置
    if (match.type === 'horizontal') {
      for (let x = match.x; x < match.x + match.length; x++) {
        gameState.grid[match.y][x].marked = true;
      }
    } else {
      for (let y = match.y; y < match.y + match.length; y++) {
        gameState.grid[y][match.x].marked = true;
      }
    }
  });
  
  // 更新总分
  gameState.score += Math.floor(matchScore);
  
  // 执行消除动画和网格更新
  setTimeout(() => {
    removeMarkedCells();
    fillEmptyCells();
    // 检查连锁反应
    if (!checkAndProcessMatches()) {
      // 连锁结束
      renderGame();
    }
  }, 300);
}

// 移除标记的消除单元格
function removeMarkedCells() {
  gameState.grid.forEach(row => {
    row.forEach(cell => {
      if (cell.marked) {
        cell.type = null;
        cell.color = '#F5F5F5';
        cell.marked = false;
      }
    });
  });
}

// 填充空白单元格
function fillEmptyCells() {
  const size = { rows: gameState.grid.length, cols: gameState.grid[0].length };
  const currentLevel = config.levels[gameState.level - 1];
  
  // 从下往上填充每一列
  for (let x = 0; x < size.cols; x++) {
    // 下落现有单元格
    for (let y = size.rows - 1; y > 0; y--) {
      if (!gameState.grid[y][x].type) {
        for (let yy = y - 1; yy >= 0; yy--) {
          if (gameState.grid[yy][x].type) {
            // 下落单元格
            gameState.grid[y][x] = gameState.grid[yy][x];
            gameState.grid[yy][x] = { type: null, color: '#F5F5F5' };
            break;
          }
        }
      }
    }
    
    // 顶部填充新单元格
    for (let y = 0; y < size.rows; y++) {
      if (!gameState.grid[y][x].type) {
        const typeId = Math.floor(Math.random() * currentLevel.petTypes);
        gameState.grid[y][x] = { 
          type: config.pets[typeId].type, 
          color: config.pets[typeId].color 
        };
      }
    }
  }
}

// 添加触摸事件处理
function setupTouchEvents() {
  let startX, startY, selectedCell = null;
  
  canvas.addEventListener('touchstart', (e) => {
    const touch = e.touches[0];
    const rect = canvas.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    selectedCell = getGridCellFromPoint(x, y);
  });
  
  canvas.addEventListener('touchend', (e) => {
    if (!selectedCell) return;
    const touch = e.changedTouches[0];
    const rect = canvas.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    const targetCell = getGridCellFromPoint(x, y);
    
    // 检查是否是相邻单元格
    if (targetCell && isAdjacent(selectedCell, targetCell)) {
      swapCells(selectedCell, targetCell);
      // 如果没有匹配则交换回来
      if (!checkAndProcessMatches()) {
        setTimeout(() => swapCells(selectedCell, targetCell), 300);
      }
    }
    selectedCell = null;
  });
}

// 从坐标获取网格单元格
function getGridCellFromPoint(x, y) {
  const cellSize = Math.min(canvas.width / 10, canvas.height * 0.7 / 8);
  const startX = (canvas.width - cellSize * 10) / 2;
  const startY = canvas.height * 0.15;
  
  if (x < startX || x > startX + cellSize * 10 || y < startY || y > startY + cellSize * 8) {
    return null;
  }
  
  return {
    x: Math.floor((x - startX) / cellSize),
    y: Math.floor((y - startY) / cellSize)
  };
}

// 检查单元格是否相邻
function isAdjacent(cell1, cell2) {
  const dx = Math.abs(cell1.x - cell2.x);
  const dy = Math.abs(cell1.y - cell2.y);
  return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
}

// 交换单元格
function swapCells(cell1, cell2) {
  const temp = gameState.grid[cell1.y][cell1.x];
  gameState.grid[cell1.y][cell1.x] = gameState.grid[cell2.y][cell2.x];
  gameState.grid[cell2.y][cell2.x] = temp;
  renderGame();
}

// 初始化游戏
function initGame() {
  systemInfo = tt.getSystemInfoSync();
  canvas = tt.createCanvas();
  ctx = canvas.getContext('2d');
  canvas.width = systemInfo.windowWidth;
  canvas.height = systemInfo.windowHeight;
  
  // 初始化网格
  gameState.grid = generateGrid(config.levels[gameState.level - 1].petTypes);
  // 设置触摸事件
  setupTouchEvents();
  renderGame();
}

// ... existing code ...