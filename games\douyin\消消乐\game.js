// 萌宠消消乐游戏 - 基于抖音原生开发
// 参考文档: https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/guide/minigame/introduction

const config = require('./config.js');

// 获取系统信息
const systemInfo = tt.getSystemInfoSync();
const canvas = tt.createCanvas();
const ctx = canvas.getContext('2d');
canvas.width = systemInfo.windowWidth;
canvas.height = systemInfo.windowHeight;

// 游戏常量
const GRID_ROWS = 10;
const GRID_COLS = 8;
const CELL_SIZE = Math.min(
  (systemInfo.windowWidth - 40) / GRID_COLS,
  (systemInfo.windowHeight * 0.7 - 40) / GRID_ROWS
);

// 游戏状态枚举
const GameState = {
  LOADING: 'loading',
  MENU: 'menu',
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'game_over',
  LEVEL_COMPLETE: 'level_complete'
};

// 特殊方块类型
const SpecialType = {
  NONE: 'none',
  ROCKET: 'rocket',
  BOMB: 'bomb'
};

// 游戏主类
class PetMatchGame {
  constructor() {
    this.gameState = GameState.LOADING;
    this.currentLevel = 1;
    this.score = 0;
    this.combo = 0;
    this.comboTimer = 0;
    this.grid = [];
    this.selectedCell = null;
    this.isAnimating = false;
    this.selectedProp = null;
    this.reviveCount = 0;
    this.maxRevives = 2;
    this.props = {
      refresh: 3,
      bomb: 2,
      clear: 1,
      levelDown: 1
    };

    // 资源管理
    this.images = {};
    this.sounds = {};
    this.loadedResources = 0;
    this.totalResources = 0;

    // 界面布局
    this.layout = this.calculateLayout();

    // 性能优化相关
    this.lastFrameTime = 0;
    this.frameCount = 0;
    this.fps = 60;

    // 对象池
    this.particlePool = [];
    this.animationPool = [];

    // 初始化游戏
    this.init();
  }

  // 计算界面布局
  calculateLayout() {
    const width = systemInfo.windowWidth;
    const height = systemInfo.windowHeight;

    return {
      // 顶部统计栏 (15%高度)
      topBar: {
        x: 0,
        y: 0,
        width: width,
        height: height * 0.15
      },
      // 中部网格区域 (70%高度)
      gameArea: {
        x: (width - GRID_COLS * CELL_SIZE) / 2,
        y: height * 0.15,
        width: GRID_COLS * CELL_SIZE,
        height: height * 0.7,
        cellSize: CELL_SIZE
      },
      // 底部道具栏 (15%高度)
      bottomBar: {
        x: 0,
        y: height * 0.85,
        width: width,
        height: height * 0.15
      }
    };
  }

  // 初始化游戏
  async init() {
    console.log('初始化萌宠消消乐游戏...');
    await this.loadResources();
    this.initGrid();
    this.gameState = GameState.MENU;
    this.gameLoop();
  }

  // 加载资源
  async loadResources() {
    const petTypes = config.pets.slice(0, config.levels[this.currentLevel - 1].petTypes);
    this.totalResources = petTypes.length + 10; // 萌宠图片 + 其他资源

    // 加载萌宠图片
    for (const pet of petTypes) {
      await this.loadImage(`animal/${pet.type}.png`, pet.type);
    }

    // 加载UI图片
    await this.loadImage('images/title.png', 'title');
    await this.loadImage('images/button/start.png', 'startBtn');
    await this.loadImage('images/extra/rocket.png', 'rocket');
    await this.loadImage('images/extra/bomb.png', 'bomb');

    // 加载道具图片
    await this.loadImage('images/prop/refresh_card.png', 'refreshCard');
    await this.loadImage('images/prop/bomb_card.png', 'bombCard');
    await this.loadImage('images/prop/clear_card.png', 'clearCard');
    await this.loadImage('images/prop/level_down_card.png', 'levelDownCard');

    // 加载音效
    await this.loadSound('audios/background.mp3', 'background');
    await this.loadSound('audios/bomb.mp3', 'bomb');
    await this.loadSound('audios/good.mp3', 'good');
    await this.loadSound('audios/win.mp3', 'win');
    await this.loadSound('audios/lose.mp3', 'lose');

    console.log('资源加载完成');
  }

  // 加载单个图片
  loadImage(src, key) {
    return new Promise((resolve) => {
      const image = tt.createImage();
      image.src = src;
      image.onload = () => {
        this.images[key] = image;
        this.loadedResources++;
        resolve();
      };
      image.onerror = () => {
        console.warn(`图片加载失败: ${src}`);
        this.loadedResources++;
        resolve();
      };
    });
  }

  // 加载音效
  loadSound(src, key) {
    return new Promise((resolve) => {
      try {
        const audio = tt.createInnerAudioContext();
        audio.src = src;
        audio.onCanplay = () => {
          this.sounds[key] = audio;
          this.loadedResources++;
          resolve();
        };
        audio.onError = () => {
          console.warn(`音效加载失败: ${src}`);
          this.loadedResources++;
          resolve();
        };
      } catch (error) {
        console.warn(`音效加载失败: ${src}`, error);
        this.loadedResources++;
        resolve();
      }
    });
  }

  // 播放音效
  playSound(key) {
    if (this.sounds[key]) {
      try {
        this.sounds[key].stop();
        this.sounds[key].play();
      } catch (error) {
        console.warn(`音效播放失败: ${key}`, error);
      }
    }
  }

  // 初始化网格
  initGrid() {
    this.grid = [];
    const currentLevelConfig = config.levels[this.currentLevel - 1];
    const availablePets = config.pets.slice(0, currentLevelConfig.petTypes);

    // 创建空网格
    for (let row = 0; row < GRID_ROWS; row++) {
      this.grid[row] = [];
      for (let col = 0; col < GRID_COLS; col++) {
        this.grid[row][col] = {
          type: null,
          special: SpecialType.NONE,
          x: col,
          y: row,
          animating: false
        };
      }
    }

    // 填充网格，确保无初始3连消
    this.fillGridSafely(availablePets);
  }

  // 安全填充网格（防止初始3连消）
  fillGridSafely(availablePets) {
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        if (!this.grid[row][col].type) {
          let attempts = 0;
          let petType;

          do {
            petType = availablePets[Math.floor(Math.random() * availablePets.length)].type;
            attempts++;
          } while (this.wouldCreateMatch(row, col, petType) && attempts < 50);

          // 如果50次尝试后仍然会产生匹配，选择一个安全的类型
          if (attempts >= 50) {
            petType = this.findSafePetType(row, col, availablePets);
          }

          this.grid[row][col].type = petType;
        }
      }
    }
  }

  // 检查放置某个萌宠类型是否会产生3连消
  wouldCreateMatch(row, col, petType) {
    // 检查水平方向
    let horizontalCount = 1;

    // 向左检查
    for (let c = col - 1; c >= 0 && this.grid[row][c].type === petType; c--) {
      horizontalCount++;
    }

    // 向右检查
    for (let c = col + 1; c < GRID_COLS && this.grid[row][c].type === petType; c++) {
      horizontalCount++;
    }

    if (horizontalCount >= 3) return true;

    // 检查垂直方向
    let verticalCount = 1;

    // 向上检查
    for (let r = row - 1; r >= 0 && this.grid[r][col].type === petType; r--) {
      verticalCount++;
    }

    // 向下检查
    for (let r = row + 1; r < GRID_ROWS && this.grid[r][col].type === petType; r++) {
      verticalCount++;
    }

    return verticalCount >= 3;
  }

  // 找到一个安全的萌宠类型
  findSafePetType(row, col, availablePets) {
    for (const pet of availablePets) {
      if (!this.wouldCreateMatch(row, col, pet.type)) {
        return pet.type;
      }
    }
    // 如果所有类型都会产生匹配，返回第一个（这种情况极少发生）
    return availablePets[0].type;
  }

  // 检查网格中是否有可能的移动
  hasValidMoves() {
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        // 检查与右边交换
        if (col < GRID_COLS - 1) {
          if (this.wouldSwapCreateMatch(row, col, row, col + 1)) {
            return true;
          }
        }

        // 检查与下面交换
        if (row < GRID_ROWS - 1) {
          if (this.wouldSwapCreateMatch(row, col, row + 1, col)) {
            return true;
          }
        }
      }
    }
    return false;
  }

  // 检查交换两个位置是否会产生匹配
  wouldSwapCreateMatch(row1, col1, row2, col2) {
    // 临时交换
    const temp = this.grid[row1][col1].type;
    this.grid[row1][col1].type = this.grid[row2][col2].type;
    this.grid[row2][col2].type = temp;

    // 检查是否产生匹配
    const hasMatch = this.findMatches().length > 0;

    // 恢复交换
    this.grid[row2][col2].type = this.grid[row1][col1].type;
    this.grid[row1][col1].type = temp;

    return hasMatch;
  }

  // 重新填充空格子
  refillGrid() {
    const currentLevelConfig = config.levels[this.currentLevel - 1];
    const availablePets = config.pets.slice(0, currentLevelConfig.petTypes);

    // 让萌宠下落
    for (let col = 0; col < GRID_COLS; col++) {
      let writeIndex = GRID_ROWS - 1;

      // 从底部向上扫描，将非空格子移到底部
      for (let row = GRID_ROWS - 1; row >= 0; row--) {
        if (this.grid[row][col].type) {
          if (row !== writeIndex) {
            this.grid[writeIndex][col] = { ...this.grid[row][col] };
            this.grid[row][col] = {
              type: null,
              special: SpecialType.NONE,
              x: col,
              y: row,
              animating: false
            };
          }
          writeIndex--;
        }
      }

      // 填充顶部空格子
      for (let row = writeIndex; row >= 0; row--) {
        let petType;
        let attempts = 0;

        do {
          petType = availablePets[Math.floor(Math.random() * availablePets.length)].type;
          attempts++;
        } while (this.wouldCreateMatch(row, col, petType) && attempts < 20);

        this.grid[row][col] = {
          type: petType,
          special: SpecialType.NONE,
          x: col,
          y: row,
          animating: true
        };
      }
    }
  }

  // 查找所有匹配
  findMatches() {
    const matches = [];
    const visited = Array(GRID_ROWS).fill().map(() => Array(GRID_COLS).fill(false));

    // 查找水平匹配
    for (let row = 0; row < GRID_ROWS; row++) {
      let count = 1;
      let currentType = this.grid[row][0].type;

      for (let col = 1; col < GRID_COLS; col++) {
        if (this.grid[row][col].type === currentType && currentType) {
          count++;
        } else {
          if (count >= 3) {
            const match = {
              type: 'horizontal',
              cells: [],
              petType: currentType,
              length: count
            };

            for (let c = col - count; c < col; c++) {
              match.cells.push({ row, col: c });
              visited[row][c] = true;
            }
            matches.push(match);
          }
          count = 1;
          currentType = this.grid[row][col].type;
        }
      }

      // 检查行末尾
      if (count >= 3) {
        const match = {
          type: 'horizontal',
          cells: [],
          petType: currentType,
          length: count
        };

        for (let c = GRID_COLS - count; c < GRID_COLS; c++) {
          match.cells.push({ row, col: c });
          visited[row][c] = true;
        }
        matches.push(match);
      }
    }

    // 查找垂直匹配
    for (let col = 0; col < GRID_COLS; col++) {
      let count = 1;
      let currentType = this.grid[0][col].type;

      for (let row = 1; row < GRID_ROWS; row++) {
        if (this.grid[row][col].type === currentType && currentType) {
          count++;
        } else {
          if (count >= 3) {
            const match = {
              type: 'vertical',
              cells: [],
              petType: currentType,
              length: count
            };

            for (let r = row - count; r < row; r++) {
              if (!visited[r][col]) {
                match.cells.push({ row: r, col });
              }
            }

            if (match.cells.length >= 3) {
              matches.push(match);
            }
          }
          count = 1;
          currentType = this.grid[row][col].type;
        }
      }

      // 检查列末尾
      if (count >= 3) {
        const match = {
          type: 'vertical',
          cells: [],
          petType: currentType,
          length: count
        };

        for (let r = GRID_ROWS - count; r < GRID_ROWS; r++) {
          if (!visited[r][col]) {
            match.cells.push({ row: r, col });
          }
        }

        if (match.cells.length >= 3) {
          matches.push(match);
        }
      }
    }

    return matches;
  }

  // 处理匹配消除
  processMatches(matches) {
    let totalScore = 0;
    const specialsToCreate = [];

    for (const match of matches) {
      // 计算基础分数
      const baseScore = config.balance.scoreValues[Math.min(match.length, 5)] || 50;
      let matchScore = baseScore;

      // 应用连击倍率
      if (this.combo >= 5) {
        matchScore *= config.balance.comboMultipliers.high;
      } else if (this.combo >= 3) {
        matchScore *= config.balance.comboMultipliers.base;
      }

      totalScore += matchScore;

      // 根据匹配长度生成特殊方块
      if (match.length === 4) {
        // 4连消生成火箭
        const centerCell = match.cells[Math.floor(match.cells.length / 2)];
        specialsToCreate.push({
          row: centerCell.row,
          col: centerCell.col,
          type: SpecialType.ROCKET,
          direction: match.type === 'horizontal' ? 'vertical' : 'horizontal'
        });
      } else if (match.length >= 5) {
        // 5连消生成炸弹
        const centerCell = match.cells[Math.floor(match.cells.length / 2)];
        specialsToCreate.push({
          row: centerCell.row,
          col: centerCell.col,
          type: SpecialType.BOMB
        });
      }

      // 清除匹配的格子
      for (const cell of match.cells) {
        this.grid[cell.row][cell.col].type = null;
        this.grid[cell.row][cell.col].special = SpecialType.NONE;
        this.grid[cell.row][cell.col].animating = true;
      }
    }

    // 创建特殊方块
    for (const special of specialsToCreate) {
      if (!this.grid[special.row][special.col].type) {
        this.grid[special.row][special.col].type = 'special';
        this.grid[special.row][special.col].special = special.type;
        this.grid[special.row][special.col].direction = special.direction;
      }
    }

    // 更新分数和连击
    this.score += totalScore;
    this.combo++;
    this.comboTimer = Date.now() + 2000; // 2秒连击时间

    // 播放消除音效
    if (matches.length > 0) {
      this.playSound('good');

      // 连击音效
      if (this.combo >= 5) {
        this.playSound('win');
      }
    }

    return totalScore;
  }

  // 处理特殊方块效果
  processSpecialEffects(row, col) {
    const cell = this.grid[row][col];
    const cellsToRemove = [];

    if (cell.special === SpecialType.ROCKET) {
      // 火箭消除整行或整列
      if (cell.direction === 'horizontal') {
        // 消除整行
        for (let c = 0; c < GRID_COLS; c++) {
          cellsToRemove.push({ row, col: c });
        }
      } else {
        // 消除整列
        for (let r = 0; r < GRID_ROWS; r++) {
          cellsToRemove.push({ row: r, col });
        }
      }
      this.score += 100;
      this.playSound('good');
    } else if (cell.special === SpecialType.BOMB) {
      // 炸弹消除3x3区域
      for (let r = Math.max(0, row - 1); r <= Math.min(GRID_ROWS - 1, row + 1); r++) {
        for (let c = Math.max(0, col - 1); c <= Math.min(GRID_COLS - 1, col + 1); c++) {
          cellsToRemove.push({ row: r, col: c });
        }
      }
      this.score += 200;
      this.playSound('bomb');
    }

    // 清除受影响的格子
    for (const cellPos of cellsToRemove) {
      this.grid[cellPos.row][cellPos.col].type = null;
      this.grid[cellPos.row][cellPos.col].special = SpecialType.NONE;
      this.grid[cellPos.row][cellPos.col].animating = true;
    }

    return cellsToRemove.length;
  }

  // 处理触摸开始
  handleTouchStart(x, y) {
    if (this.gameState !== GameState.PLAYING || this.isAnimating) return;

    const cellPos = this.getCellFromPosition(x, y);
    if (cellPos && this.grid[cellPos.row][cellPos.col].type) {
      this.selectedCell = cellPos;
      this.touchStartPos = { x, y };
    }
  }

  // 处理触摸移动
  handleTouchMove(x, y) {
    if (!this.selectedCell || !this.touchStartPos) return;

    const deltaX = x - this.touchStartPos.x;
    const deltaY = y - this.touchStartPos.y;
    const threshold = CELL_SIZE * 0.3;

    if (Math.abs(deltaX) > threshold || Math.abs(deltaY) > threshold) {
      let targetRow = this.selectedCell.row;
      let targetCol = this.selectedCell.col;

      // 确定拖拽方向
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平拖拽
        targetCol += deltaX > 0 ? 1 : -1;
      } else {
        // 垂直拖拽
        targetRow += deltaY > 0 ? 1 : -1;
      }

      // 检查目标位置是否有效
      if (targetRow >= 0 && targetRow < GRID_ROWS &&
          targetCol >= 0 && targetCol < GRID_COLS) {
        this.attemptSwap(this.selectedCell.row, this.selectedCell.col, targetRow, targetCol);
      }

      this.selectedCell = null;
      this.touchStartPos = null;
    }
  }

  // 处理触摸结束
  handleTouchEnd(x, y) {
    if (this.selectedCell && this.touchStartPos) {
      const cellPos = this.getCellFromPosition(x, y);

      if (cellPos &&
          (cellPos.row !== this.selectedCell.row || cellPos.col !== this.selectedCell.col)) {
        // 检查是否是相邻格子
        const rowDiff = Math.abs(cellPos.row - this.selectedCell.row);
        const colDiff = Math.abs(cellPos.col - this.selectedCell.col);

        if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
          this.attemptSwap(this.selectedCell.row, this.selectedCell.col, cellPos.row, cellPos.col);
        }
      }
    }

    this.selectedCell = null;
    this.touchStartPos = null;
  }

  // 根据屏幕坐标获取网格位置
  getCellFromPosition(x, y) {
    const gameArea = this.layout.gameArea;

    if (x < gameArea.x || x > gameArea.x + gameArea.width ||
        y < gameArea.y || y > gameArea.y + gameArea.height) {
      return null;
    }

    const col = Math.floor((x - gameArea.x) / gameArea.cellSize);
    const row = Math.floor((y - gameArea.y) / gameArea.cellSize);

    if (row >= 0 && row < GRID_ROWS && col >= 0 && col < GRID_COLS) {
      return { row, col };
    }

    return null;
  }

  // 尝试交换两个格子
  attemptSwap(row1, col1, row2, col2) {
    if (this.isAnimating) return false;

    const cell1 = this.grid[row1][col1];
    const cell2 = this.grid[row2][col2];

    // 处理特殊方块交换
    if (cell1.special !== SpecialType.NONE || cell2.special !== SpecialType.NONE) {
      this.handleSpecialSwap(row1, col1, row2, col2);
      return true;
    }

    // 临时交换
    this.grid[row1][col1] = cell2;
    this.grid[row2][col2] = cell1;

    // 检查是否产生匹配
    const matches = this.findMatches();

    if (matches.length > 0) {
      // 有效交换，开始消除流程
      this.isAnimating = true;
      this.startEliminationChain();
      return true;
    } else {
      // 无效交换，恢复原状
      this.grid[row1][col1] = cell1;
      this.grid[row2][col2] = cell2;

      // 播放无效交换动画
      this.playInvalidSwapAnimation();
      return false;
    }
  }

  // 处理特殊方块交换
  handleSpecialSwap(row1, col1, row2, col2) {
    const cell1 = this.grid[row1][col1];
    const cell2 = this.grid[row2][col2];

    this.isAnimating = true;

    if (cell1.special === SpecialType.ROCKET && cell2.special === SpecialType.ROCKET) {
      // 火箭+火箭：十字消除
      this.processCrossElimination(row1, col1);
      this.score += 300;
    } else if (cell1.special === SpecialType.BOMB && cell2.special === SpecialType.BOMB) {
      // 炸弹+炸弹：5x5爆炸
      this.processBigExplosion(row1, col1);
      this.score += 500;
    } else if (cell1.special === SpecialType.ROCKET || cell2.special === SpecialType.ROCKET) {
      // 火箭与普通格子
      const rocketPos = cell1.special === SpecialType.ROCKET ? {row: row1, col: col1} : {row: row2, col: col2};
      this.processSpecialEffects(rocketPos.row, rocketPos.col);
    } else if (cell1.special === SpecialType.BOMB || cell2.special === SpecialType.BOMB) {
      // 炸弹与普通格子
      const bombPos = cell1.special === SpecialType.BOMB ? {row: row1, col: col1} : {row: row2, col: col2};
      this.processSpecialEffects(bombPos.row, bombPos.col);
    }

    setTimeout(() => {
      this.refillGrid();
      this.checkForNewMatches();
    }, 500);
  }

  // 十字消除
  processCrossElimination(row, col) {
    // 消除整行
    for (let c = 0; c < GRID_COLS; c++) {
      this.grid[row][c].type = null;
      this.grid[row][c].special = SpecialType.NONE;
      this.grid[row][c].animating = true;
    }

    // 消除整列
    for (let r = 0; r < GRID_ROWS; r++) {
      this.grid[r][col].type = null;
      this.grid[r][col].special = SpecialType.NONE;
      this.grid[r][col].animating = true;
    }
  }

  // 大爆炸（5x5）
  processBigExplosion(row, col) {
    for (let r = Math.max(0, row - 2); r <= Math.min(GRID_ROWS - 1, row + 2); r++) {
      for (let c = Math.max(0, col - 2); c <= Math.min(GRID_COLS - 1, col + 2); c++) {
        this.grid[r][c].type = null;
        this.grid[r][c].special = SpecialType.NONE;
        this.grid[r][c].animating = true;
      }
    }
  }

  // 开始消除连锁
  startEliminationChain() {
    const matches = this.findMatches();

    if (matches.length > 0) {
      this.processMatches(matches);

      setTimeout(() => {
        this.refillGrid();
        setTimeout(() => {
          this.checkForNewMatches();
        }, 300);
      }, 500);
    } else {
      this.isAnimating = false;
      this.checkGameState();
    }
  }

  // 检查新的匹配
  checkForNewMatches() {
    const matches = this.findMatches();

    if (matches.length > 0) {
      this.startEliminationChain();
    } else {
      this.isAnimating = false;
      this.checkGameState();
    }
  }

  // 播放无效交换动画
  playInvalidSwapAnimation() {
    // 简单的震动效果提示
    if (tt.vibrateShort) {
      tt.vibrateShort();
    }
    console.log('无效交换');
  }

  // 检查游戏状态
  checkGameState() {
    // 检查连击超时
    if (this.comboTimer > 0 && Date.now() > this.comboTimer) {
      this.combo = 0;
      this.comboTimer = 0;
    }

    // 检查胜利条件
    const currentLevelConfig = config.levels[this.currentLevel - 1];
    if (this.score >= currentLevelConfig.targetScore) {
      this.gameState = GameState.LEVEL_COMPLETE;
      this.playSound('win');
      return;
    }

    // 检查是否有可用移动
    if (!this.hasValidMoves()) {
      if (this.reviveCount < this.maxRevives) {
        this.showReviveDialog();
      } else {
        this.gameState = GameState.GAME_OVER;
        this.playSound('lose');
      }
    }
  }

  // 显示复活对话框
  showReviveDialog() {
    // 简化版复活机制，直接刷新网格
    this.reviveCount++;
    this.useRefreshProp();
    this.playSound('good');
  }

  // 重置复活次数
  resetReviveCount() {
    this.reviveCount = 0;
  }

  // 渲染游戏
  render() {
    // 清空画布
    ctx.fillStyle = '#E5EBF6';
    ctx.fillRect(0, 0, systemInfo.windowWidth, systemInfo.windowHeight);

    switch (this.gameState) {
      case GameState.LOADING:
        this.renderLoading();
        break;
      case GameState.MENU:
        this.renderMenu();
        break;
      case GameState.PLAYING:
        this.renderGame();
        break;
      case GameState.LEVEL_COMPLETE:
        this.renderLevelComplete();
        break;
      case GameState.GAME_OVER:
        this.renderGameOver();
        break;
    }
  }

  // 渲染加载界面
  renderLoading() {
    const progress = this.loadedResources / this.totalResources;
    const barWidth = systemInfo.windowWidth * 0.6;
    const barHeight = 20;
    const x = (systemInfo.windowWidth - barWidth) / 2;
    const y = systemInfo.windowHeight / 2;

    // 绘制进度条背景
    ctx.fillStyle = '#CCCCCC';
    ctx.fillRect(x, y, barWidth, barHeight);

    // 绘制进度条
    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(x, y, barWidth * progress, barHeight);

    // 绘制文字
    ctx.fillStyle = '#333333';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('加载中...', systemInfo.windowWidth / 2, y - 30);
    ctx.fillText(`${Math.round(progress * 100)}%`, systemInfo.windowWidth / 2, y + 50);
  }

  // 渲染菜单界面
  renderMenu() {
    // 绘制标题
    if (this.images.title) {
      const titleWidth = systemInfo.windowWidth * 0.8;
      const titleHeight = titleWidth * 0.3;
      ctx.drawImage(
        this.images.title,
        (systemInfo.windowWidth - titleWidth) / 2,
        systemInfo.windowHeight * 0.2,
        titleWidth,
        titleHeight
      );
    }

    // 绘制开始按钮
    const buttonWidth = 200;
    const buttonHeight = 60;
    const buttonX = (systemInfo.windowWidth - buttonWidth) / 2;
    const buttonY = systemInfo.windowHeight * 0.6;

    ctx.fillStyle = '#FF6B6B';
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('开始游戏', systemInfo.windowWidth / 2, buttonY + 38);
  }

  // 渲染游戏界面
  renderGame() {
    this.renderTopBar();
    this.renderGameGrid();
    this.renderBottomBar();
  }

  // 渲染顶部统计栏
  renderTopBar() {
    const topBar = this.layout.topBar;
    const currentLevelConfig = config.levels[this.currentLevel - 1];

    // 背景
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(topBar.x, topBar.y, topBar.width, topBar.height);

    // 关卡标题
    ctx.fillStyle = '#333333';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      currentLevelConfig.title,
      topBar.width / 2,
      topBar.y + 25
    );

    // 分数和目标
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(
      `目标: ${currentLevelConfig.targetScore}`,
      20,
      topBar.y + 50
    );

    ctx.textAlign = 'right';
    ctx.fillText(
      `当前: ${this.score}`,
      topBar.width - 20,
      topBar.y + 50
    );

    // 进度条
    const progressWidth = topBar.width - 40;
    const progressHeight = 8;
    const progressX = 20;
    const progressY = topBar.y + 60;
    const progress = Math.min(this.score / currentLevelConfig.targetScore, 1);

    ctx.fillStyle = '#E0E0E0';
    ctx.fillRect(progressX, progressY, progressWidth, progressHeight);

    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(progressX, progressY, progressWidth * progress, progressHeight);

    // 连击显示
    if (this.combo > 0) {
      ctx.fillStyle = '#FF4500';
      ctx.font = 'bold 18px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `🔥 连击 x${this.combo}`,
        topBar.width / 2,
        topBar.y + 85
      );
    }
  }

  // 渲染游戏网格
  renderGameGrid() {
    const gameArea = this.layout.gameArea;

    // 绘制网格背景
    ctx.fillStyle = '#F5F5F5';
    ctx.fillRect(gameArea.x, gameArea.y, gameArea.width, gameArea.height);

    // 绘制网格线
    ctx.strokeStyle = '#D3D3D3';
    ctx.lineWidth = 1;

    for (let row = 0; row <= GRID_ROWS; row++) {
      const y = gameArea.y + row * gameArea.cellSize;
      ctx.beginPath();
      ctx.moveTo(gameArea.x, y);
      ctx.lineTo(gameArea.x + gameArea.width, y);
      ctx.stroke();
    }

    for (let col = 0; col <= GRID_COLS; col++) {
      const x = gameArea.x + col * gameArea.cellSize;
      ctx.beginPath();
      ctx.moveTo(x, gameArea.y);
      ctx.lineTo(x, gameArea.y + gameArea.height);
      ctx.stroke();
    }

    // 绘制萌宠
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        const cell = this.grid[row][col];
        if (cell.type) {
          this.renderCell(row, col, cell);
        }
      }
    }

    // 绘制选中效果
    if (this.selectedCell) {
      const x = gameArea.x + this.selectedCell.col * gameArea.cellSize;
      const y = gameArea.y + this.selectedCell.row * gameArea.cellSize;

      ctx.strokeStyle = '#FFA07A';
      ctx.lineWidth = 3;
      ctx.strokeRect(x, y, gameArea.cellSize, gameArea.cellSize);
    }
  }

  // 渲染单个格子
  renderCell(row, col, cell) {
    const gameArea = this.layout.gameArea;
    const x = gameArea.x + col * gameArea.cellSize;
    const y = gameArea.y + row * gameArea.cellSize;
    const size = gameArea.cellSize - 4; // 留出边距

    // 绘制背景色
    if (cell.type !== 'special') {
      const petConfig = config.pets.find(p => p.type === cell.type);
      if (petConfig) {
        ctx.fillStyle = petConfig.color;
        ctx.fillRect(x + 2, y + 2, size, size);
      }
    }

    // 绘制萌宠图片或特殊方块
    if (cell.special === SpecialType.NONE && cell.type !== 'special') {
      // 普通萌宠
      const image = this.images[cell.type];
      if (image) {
        ctx.drawImage(image, x + 2, y + 2, size, size);
      }
    } else if (cell.special === SpecialType.ROCKET) {
      // 火箭
      ctx.fillStyle = config.props.rocket.color;
      ctx.fillRect(x + 2, y + 2, size, size);

      const rocketImage = this.images.rocket;
      if (rocketImage) {
        ctx.drawImage(rocketImage, x + 2, y + 2, size, size);
      }
    } else if (cell.special === SpecialType.BOMB) {
      // 炸弹
      ctx.fillStyle = config.props.bomb.color;
      ctx.fillRect(x + 2, y + 2, size, size);

      const bombImage = this.images.bomb;
      if (bombImage) {
        ctx.drawImage(bombImage, x + 2, y + 2, size, size);
      }
    }

    // 绘制动画效果
    if (cell.animating) {
      ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.fillRect(x + 2, y + 2, size, size);
    }
  }

  // 渲染底部道具栏
  renderBottomBar() {
    const bottomBar = this.layout.bottomBar;

    // 背景
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(bottomBar.x, bottomBar.y, bottomBar.width, bottomBar.height);

    // 道具卡片
    const propWidth = bottomBar.width / 4 - 10;
    const propHeight = bottomBar.height - 20;
    const props = [
      { key: 'refresh', count: this.props.refresh, image: 'refreshCard' },
      { key: 'bomb', count: this.props.bomb, image: 'bombCard' },
      { key: 'clear', count: this.props.clear, image: 'clearCard' },
      { key: 'levelDown', count: this.props.levelDown, image: 'levelDownCard' }
    ];

    for (let i = 0; i < props.length; i++) {
      const prop = props[i];
      const x = bottomBar.x + 5 + i * (propWidth + 10);
      const y = bottomBar.y + 10;

      // 绘制道具背景
      ctx.fillStyle = prop.count > 0 ? '#E8F5E8' : '#F0F0F0';
      ctx.fillRect(x, y, propWidth, propHeight);

      // 绘制道具图片
      const image = this.images[prop.image];
      if (image) {
        const imageSize = Math.min(propWidth, propHeight) * 0.6;
        const imageX = x + (propWidth - imageSize) / 2;
        const imageY = y + (propHeight - imageSize) / 2 - 10;
        ctx.drawImage(image, imageX, imageY, imageSize, imageSize);
      }

      // 绘制数量
      ctx.fillStyle = prop.count > 0 ? '#333333' : '#CCCCCC';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(
        `×${prop.count}`,
        x + propWidth / 2,
        y + propHeight - 5
      );
    }
  }

  // 渲染关卡完成界面
  renderLevelComplete() {
    // 半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, systemInfo.windowWidth, systemInfo.windowHeight);

    // 完成提示
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      '关卡完成！',
      systemInfo.windowWidth / 2,
      systemInfo.windowHeight / 2 - 50
    );

    ctx.font = '20px Arial';
    ctx.fillText(
      `得分: ${this.score}`,
      systemInfo.windowWidth / 2,
      systemInfo.windowHeight / 2
    );

    // 继续按钮
    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonX = (systemInfo.windowWidth - buttonWidth) / 2;
    const buttonY = systemInfo.windowHeight / 2 + 50;

    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 20px Arial';
    ctx.fillText('下一关', systemInfo.windowWidth / 2, buttonY + 32);
  }

  // 渲染游戏结束界面
  renderGameOver() {
    // 半透明遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, systemInfo.windowWidth, systemInfo.windowHeight);

    // 游戏结束提示
    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      '游戏结束',
      systemInfo.windowWidth / 2,
      systemInfo.windowHeight / 2 - 50
    );

    ctx.font = '20px Arial';
    ctx.fillText(
      `最终得分: ${this.score}`,
      systemInfo.windowWidth / 2,
      systemInfo.windowHeight / 2
    );

    // 重新开始按钮
    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonX = (systemInfo.windowWidth - buttonWidth) / 2;
    const buttonY = systemInfo.windowHeight / 2 + 50;

    ctx.fillStyle = '#FF6B6B';
    ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

    ctx.fillStyle = '#FFFFFF';
    ctx.font = 'bold 20px Arial';
    ctx.fillText('重新开始', systemInfo.windowWidth / 2, buttonY + 32);
  }

  // 使用道具
  useProp(propType, targetRow = -1, targetCol = -1) {
    if (this.props[propType] <= 0 || this.isAnimating) return false;

    this.props[propType]--;
    this.isAnimating = true;

    switch (propType) {
      case 'refresh':
        this.useRefreshProp();
        break;
      case 'bomb':
        if (targetRow >= 0 && targetCol >= 0) {
          this.useBombProp(targetRow, targetCol);
        }
        break;
      case 'clear':
        this.useClearProp();
        break;
      case 'levelDown':
        this.useLevelDownProp();
        break;
    }

    return true;
  }

  // 使用刷新道具
  useRefreshProp() {
    // 随机洗牌网格
    const allCells = [];

    // 收集所有非空格子的类型
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        if (this.grid[row][col].type && this.grid[row][col].special === SpecialType.NONE) {
          allCells.push(this.grid[row][col].type);
        }
      }
    }

    // 打乱数组
    for (let i = allCells.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [allCells[i], allCells[j]] = [allCells[j], allCells[i]];
    }

    // 重新分配到网格
    let cellIndex = 0;
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        if (this.grid[row][col].type && this.grid[row][col].special === SpecialType.NONE) {
          this.grid[row][col].type = allCells[cellIndex];
          this.grid[row][col].animating = true;
          cellIndex++;
        }
      }
    }

    setTimeout(() => {
      this.isAnimating = false;
      this.checkGameState();
    }, 1000);
  }

  // 使用炸弹道具
  useBombProp(targetRow, targetCol) {
    // 5×5爆炸范围
    for (let r = Math.max(0, targetRow - 2); r <= Math.min(GRID_ROWS - 1, targetRow + 2); r++) {
      for (let c = Math.max(0, targetCol - 2); c <= Math.min(GRID_COLS - 1, targetCol + 2); c++) {
        this.grid[r][c].type = null;
        this.grid[r][c].special = SpecialType.NONE;
        this.grid[r][c].animating = true;
      }
    }

    this.score += 500;

    setTimeout(() => {
      this.refillGrid();
      setTimeout(() => {
        this.checkForNewMatches();
      }, 300);
    }, 500);
  }

  // 使用清屏道具
  useClearProp() {
    // 清除所有萌宠
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        this.grid[row][col].type = null;
        this.grid[row][col].special = SpecialType.NONE;
        this.grid[row][col].animating = true;
      }
    }

    this.score += 1000;

    setTimeout(() => {
      this.refillGrid();
      setTimeout(() => {
        this.checkForNewMatches();
      }, 300);
    }, 500);
  }

  // 使用降级道具
  useLevelDownProp() {
    const currentLevelConfig = config.levels[this.currentLevel - 1];
    const availablePets = config.pets.slice(0, Math.max(3, currentLevelConfig.petTypes - 2));

    // 将所有萌宠替换为更少种类的萌宠
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        if (this.grid[row][col].type && this.grid[row][col].special === SpecialType.NONE) {
          const randomPet = availablePets[Math.floor(Math.random() * availablePets.length)];
          this.grid[row][col].type = randomPet.type;
          this.grid[row][col].animating = true;
        }
      }
    }

    setTimeout(() => {
      this.isAnimating = false;
      this.checkGameState();
    }, 1000);
  }

  // 处理道具栏点击
  handlePropClick(x, y) {
    const bottomBar = this.layout.bottomBar;

    if (y < bottomBar.y || y > bottomBar.y + bottomBar.height) return null;

    const propWidth = bottomBar.width / 4 - 10;
    const propIndex = Math.floor((x - bottomBar.x - 5) / (propWidth + 10));

    if (propIndex >= 0 && propIndex < 4) {
      const propTypes = ['refresh', 'bomb', 'clear', 'levelDown'];
      return propTypes[propIndex];
    }

    return null;
  }

  // 游戏主循环
  gameLoop(currentTime = 0) {
    // 帧率控制
    const deltaTime = currentTime - this.lastFrameTime;

    if (deltaTime >= 1000 / 60) { // 限制为60FPS
      this.update(deltaTime);
      this.render();
      this.lastFrameTime = currentTime;
      this.frameCount++;

      // 每秒计算一次FPS
      if (this.frameCount % 60 === 0) {
        this.fps = Math.round(1000 / deltaTime);
      }
    }

    requestAnimationFrame((time) => this.gameLoop(time));
  }

  // 游戏更新逻辑
  update(deltaTime) {
    // 更新动画状态
    for (let row = 0; row < GRID_ROWS; row++) {
      for (let col = 0; col < GRID_COLS; col++) {
        const cell = this.grid[row][col];
        if (cell.animating) {
          // 简单的动画计时器
          cell.animationTime = (cell.animationTime || 0) + deltaTime;
          if (cell.animationTime > 500) {
            cell.animating = false;
            cell.animationTime = 0;
          }
        }
      }
    }
  }

  // 处理点击事件
  handleClick(x, y) {
    switch (this.gameState) {
      case GameState.MENU:
        // 检查开始按钮
        const buttonWidth = 200;
        const buttonHeight = 60;
        const buttonX = (systemInfo.windowWidth - buttonWidth) / 2;
        const buttonY = systemInfo.windowHeight * 0.6;

        if (x >= buttonX && x <= buttonX + buttonWidth &&
            y >= buttonY && y <= buttonY + buttonHeight) {
          this.startGame();
        }
        break;

      case GameState.PLAYING:
        // 检查道具栏点击
        const propType = this.handlePropClick(x, y);
        if (propType) {
          if (propType === 'bomb') {
            // 炸弹需要选择目标
            this.selectedProp = propType;
          } else {
            this.useProp(propType);
          }
        } else if (this.selectedProp === 'bomb') {
          // 使用炸弹道具
          const cellPos = this.getCellFromPosition(x, y);
          if (cellPos) {
            this.useProp('bomb', cellPos.row, cellPos.col);
            this.selectedProp = null;
          }
        }
        break;

      case GameState.LEVEL_COMPLETE:
        // 检查下一关按钮
        const nextButtonWidth = 200;
        const nextButtonHeight = 50;
        const nextButtonX = (systemInfo.windowWidth - nextButtonWidth) / 2;
        const nextButtonY = systemInfo.windowHeight / 2 + 50;

        if (x >= nextButtonX && x <= nextButtonX + nextButtonWidth &&
            y >= nextButtonY && y <= nextButtonY + nextButtonHeight) {
          this.nextLevel();
        }
        break;

      case GameState.GAME_OVER:
        // 检查重新开始按钮
        const restartButtonWidth = 200;
        const restartButtonHeight = 50;
        const restartButtonX = (systemInfo.windowWidth - restartButtonWidth) / 2;
        const restartButtonY = systemInfo.windowHeight / 2 + 50;

        if (x >= restartButtonX && x <= restartButtonX + restartButtonWidth &&
            y >= restartButtonY && y <= restartButtonY + restartButtonHeight) {
          this.restartGame();
        }
        break;
    }
  }

  // 开始游戏
  startGame() {
    this.gameState = GameState.PLAYING;
    this.score = 0;
    this.combo = 0;
    this.currentLevel = 1;
    this.resetReviveCount();
    this.props = {
      refresh: 3,
      bomb: 2,
      clear: 1,
      levelDown: 1
    };
    this.initGrid();
    this.playSound('good');
  }

  // 下一关
  nextLevel() {
    if (this.currentLevel < config.levels.length) {
      this.currentLevel++;
      this.initGrid();
      this.gameState = GameState.PLAYING;
    } else {
      // 游戏通关
      this.gameState = GameState.GAME_OVER;
    }
  }

  // 重新开始游戏
  restartGame() {
    this.startGame();
  }
}

// 创建游戏实例
const game = new PetMatchGame();

// 添加触摸事件监听
canvas.addEventListener('touchstart', (e) => {
  e.preventDefault();
  const touch = e.touches[0];
  const rect = canvas.getBoundingClientRect();
  const x = touch.clientX - rect.left;
  const y = touch.clientY - rect.top;

  game.handleTouchStart(x, y);
  game.handleClick(x, y);
});

canvas.addEventListener('touchmove', (e) => {
  e.preventDefault();
  const touch = e.touches[0];
  const rect = canvas.getBoundingClientRect();
  const x = touch.clientX - rect.left;
  const y = touch.clientY - rect.top;

  game.handleTouchMove(x, y);
});

canvas.addEventListener('touchend', (e) => {
  e.preventDefault();
  if (e.changedTouches.length > 0) {
    const touch = e.changedTouches[0];
    const rect = canvas.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;

    game.handleTouchEnd(x, y);
  }
});

// 添加鼠标事件监听（用于开发调试）
canvas.addEventListener('mousedown', (e) => {
  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  game.handleTouchStart(x, y);
  game.handleClick(x, y);
});

canvas.addEventListener('mousemove', (e) => {
  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  game.handleTouchMove(x, y);
});

canvas.addEventListener('mouseup', (e) => {
  const rect = canvas.getBoundingClientRect();
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;

  game.handleTouchEnd(x, y);
});

// 防止页面滚动
document.addEventListener('touchmove', (e) => {
  e.preventDefault();
}, { passive: false });

// 错误处理
window.addEventListener('error', (e) => {
  console.error('游戏运行错误:', e.error);
});

// 调试信息
if (typeof console !== 'undefined') {
  console.log('萌宠消消乐游戏已启动！');
  console.log('游戏配置:', config);
  console.log('系统信息:', systemInfo);
}
